# Test Chức Năng Xóa Comment

## ✅ Đã Triển Khai

### Backend (Đã có sẵn):
- ✅ `DELETE /comments/{commentId}` - Endpoint xóa comment
- ✅ Authorization: Chỉ người tạo comment mới được xóa
- ✅ Kiểm tra `userId` trong CommentService

### Frontend (Vừa thêm):
- ✅ Method `deleteComment` trong comment-service.js
- ✅ Nút xóa (trash icon) trong CommentItem
- ✅ Logic hiển thị nút xóa: `canDelete = currentUser && comment.userId === currentUser.id && !isTemporary`
- ✅ Optimistic update khi xóa
- ✅ Confirm dialog trước khi xóa
- ✅ Error handling với rollback

## 🧪 Test Cases

### 1. Test Hiển Thị Nút Xóa
- ✅ Nút xóa chỉ hiển thị cho comment của user hiện tại
- ✅ Không hiển thị nút xóa cho comment của người khác
- ✅ Không hiển thị nút xóa cho temp comment (đang gửi)
- ✅ Không hiển thị nút xóa khi chưa đăng nhập

### 2. Test Chức Năng Xóa
- ✅ Click nút xóa → hiển thị confirm dialog
- ✅ Confirm "OK" → gọi API xóa comment
- ✅ Confirm "Cancel" → không làm gì
- ✅ Optimistic update: comment biến mất ngay lập tức
- ✅ Nếu API thành công → comment bị xóa vĩnh viễn
- ✅ Nếu API thất bại → rollback, hiển thị lại comment

### 3. Test Error Handling
- ✅ Xóa comment của người khác → "Bạn chỉ có thể xóa bình luận của chính mình"
- ✅ Comment không tồn tại → "Bình luận không tồn tại"
- ✅ Lỗi network → "Đã xảy ra lỗi khi xóa bình luận"

## 🎯 Cách Test

### 1. Test UI:
1. Đăng nhập vào manga-react
2. Vào một chapter có comment
3. Tạo comment mới
4. Kiểm tra nút xóa (trash icon) xuất hiện ở comment vừa tạo
5. Kiểm tra không có nút xóa ở comment của người khác

### 2. Test Xóa Comment:
1. Click nút xóa comment của mình
2. Confirm dialog xuất hiện
3. Click "OK" → comment biến mất
4. Refresh trang → comment đã bị xóa vĩnh viễn

### 3. Test Authorization:
1. Thử gọi API xóa comment của người khác (qua DevTools)
2. Kiểm tra nhận được lỗi "You are not authorized to delete this comment"

## 🔧 Debugging

### Kiểm tra userId:
```javascript
// Trong browser console
console.log('Current user:', user);
console.log('Comment userId:', comment.userId);
console.log('Can delete:', comment.userId === user?.id);
```

### Kiểm tra API call:
```javascript
// Trong Network tab
// Tìm request DELETE /comments/{commentId}
// Kiểm tra response status và message
```

## 🎉 Kết Quả

Chức năng xóa comment đã được triển khai hoàn chỉnh với:
- ✅ UI/UX tốt (nút xóa chỉ hiển thị khi cần)
- ✅ Security (chỉ người tạo mới xóa được)
- ✅ Performance (optimistic update)
- ✅ Error handling (rollback khi thất bại)
- ✅ User experience (confirm dialog)
